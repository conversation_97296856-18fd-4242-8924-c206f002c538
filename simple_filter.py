#!/usr/bin/env python3
"""
Simple script to filter FASTA file based on OTU IDs from CSV table.
"""

def main():
    # File paths
    otu_table_file = "OTU_table_taxonomy.csv"
    fasta_file = "cluster.cluster.otus.fa"
    output_file = "filtered_otus.fa"
    
    print("Reading OTU IDs from CSV file...")
    
    # Read OTU IDs from CSV
    otu_ids = set()
    try:
        with open(otu_table_file, 'r') as f:
            # Skip header line
            header = f.readline()
            print(f"Header: {header.strip()[:100]}...")
            
            # Read OTU IDs from first column
            for line_num, line in enumerate(f, 2):
                if line.strip():
                    parts = line.split(',')
                    if parts:
                        otu_id = parts[0].strip()
                        if otu_id:
                            otu_ids.add(otu_id)
                
                if line_num % 10000 == 0:
                    print(f"Processed {line_num} lines...")
        
        print(f"Found {len(otu_ids)} unique OTU IDs")
        print(f"Example OTUs: {list(otu_ids)[:5]}")
        
    except Exception as e:
        print(f"Error reading OTU table: {e}")
        return
    
    print("\nFiltering FASTA file...")
    
    # Filter FASTA file
    kept_count = 0
    total_count = 0
    
    try:
        with open(fasta_file, 'r') as f_in, open(output_file, 'w') as f_out:
            current_header = None
            current_sequence = []
            
            for line in f_in:
                line = line.strip()
                
                if line.startswith('>'):
                    # Process previous sequence if exists
                    if current_header is not None:
                        total_count += 1
                        otu_id = current_header[1:]  # Remove '>'
                        
                        if otu_id in otu_ids:
                            f_out.write(f">{otu_id}\n")
                            sequence = ''.join(current_sequence)
                            # Write sequence in 80-character lines
                            for i in range(0, len(sequence), 80):
                                f_out.write(sequence[i:i+80] + '\n')
                            kept_count += 1
                        
                        if total_count % 10000 == 0:
                            print(f"Processed {total_count} sequences, kept {kept_count}")
                    
                    # Start new sequence
                    current_header = line
                    current_sequence = []
                else:
                    current_sequence.append(line)
            
            # Don't forget the last sequence
            if current_header is not None:
                total_count += 1
                otu_id = current_header[1:]  # Remove '>'
                
                if otu_id in otu_ids:
                    f_out.write(f">{otu_id}\n")
                    sequence = ''.join(current_sequence)
                    for i in range(0, len(sequence), 80):
                        f_out.write(sequence[i:i+80] + '\n')
                    kept_count += 1
        
        print(f"\nFiltering complete!")
        print(f"Total sequences in FASTA: {total_count}")
        print(f"Sequences kept: {kept_count}")
        print(f"Sequences filtered out: {total_count - kept_count}")
        print(f"Output written to: {output_file}")
        
        if kept_count == 0:
            print("\nWARNING: No sequences were kept!")
            print("Checking for potential issues...")
            
            # Show some examples from both files for debugging
            print(f"\nFirst few OTU IDs from table: {list(otu_ids)[:10]}")
            
            # Read first few FASTA headers
            with open(fasta_file, 'r') as f:
                fasta_headers = []
                for line in f:
                    if line.startswith('>'):
                        fasta_headers.append(line.strip()[1:])
                        if len(fasta_headers) >= 10:
                            break
            print(f"First few FASTA headers: {fasta_headers}")
        
    except Exception as e:
        print(f"Error processing files: {e}")

if __name__ == "__main__":
    main()
