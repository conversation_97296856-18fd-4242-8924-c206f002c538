#!/usr/bin/env Rscript
# Create matching OTU table and FASTA files for PICRUSt2

cat("Creating matching files for PICRUSt2...\n")

# Read OTU table
otu_table <- read.csv("OTU_table_taxonomy.csv", header = TRUE, stringsAsFactors = FALSE)
cat("Original OTU table has", nrow(otu_table), "OTUs\n")

# Remove taxonomy columns
taxonomy_cols <- grep("taxonomy|kingdom|phylum|class|order|family|genus|species", 
                     colnames(otu_table), ignore.case = TRUE)
if (length(taxonomy_cols) > 0) {
  otu_data <- otu_table[, -taxonomy_cols]
} else {
  otu_data <- otu_table
}

# Create new sequential OTU IDs
new_otu_ids <- paste0("OTU", 1:nrow(otu_data))
cat("Creating", length(new_otu_ids), "new sequential OTU IDs\n")

# Create new OTU table with sequential IDs
new_otu_table <- data.frame(
  OTU_ID = new_otu_ids,
  otu_data[, -1],  # Remove original OTU_ID column
  stringsAsFactors = FALSE
)

# Write new OTU table
cat("Writing new OTU table...\n")
cat("# Constructed from biom file\n", file = "otu_table_sequential.tsv")
write.table(new_otu_table, "otu_table_sequential.tsv", sep = "\t", 
           quote = FALSE, row.names = FALSE, append = TRUE)

# Now create matching FASTA file with first N sequences
cat("Creating matching FASTA file...\n")
fasta_lines <- readLines("cluster.cluster.otus.fa")
fasta_headers <- which(grepl("^>", fasta_lines))

# Extract first N sequences to match the OTU table
n_otus <- nrow(otu_data)
new_fasta_lines <- character()

for (i in 1:n_otus) {
  if (i <= length(fasta_headers)) {
    # Get header and sequence for OTU i
    header_idx <- fasta_headers[i]
    
    # Find sequence lines
    if (i < length(fasta_headers)) {
      next_header_idx <- fasta_headers[i + 1]
      seq_end <- next_header_idx - 1
    } else {
      seq_end <- length(fasta_lines)
    }
    
    # Add header with new ID
    new_fasta_lines <- c(new_fasta_lines, paste0(">OTU", i))
    
    # Add sequence lines
    seq_lines <- fasta_lines[(header_idx + 1):seq_end]
    seq_lines <- seq_lines[!grepl("^>", seq_lines)]  # Remove any stray headers
    new_fasta_lines <- c(new_fasta_lines, seq_lines)
  }
  
  if (i %% 5000 == 0) {
    cat("Processed", i, "sequences...\n")
  }
}

# Write new FASTA file
writeLines(new_fasta_lines, "otus_sequential.fna")

cat("\n=== Files Created ===\n")
cat("OTU table: otu_table_sequential.tsv (", nrow(new_otu_table), "OTUs )\n")
cat("FASTA file: otus_sequential.fna (", length(grep("^>", new_fasta_lines)), "sequences )\n")

# Verify matching
table_ids <- new_otu_table$OTU_ID
fasta_ids <- sub("^>", "", new_fasta_lines[grepl("^>", new_fasta_lines)])
matches <- sum(table_ids %in% fasta_ids)

cat("\nVerification:\n")
cat("OTU table IDs:", length(table_ids), "\n")
cat("FASTA sequence IDs:", length(fasta_ids), "\n")
cat("Matching IDs:", matches, "\n")

if (matches == length(table_ids) && matches == length(fasta_ids)) {
  cat("SUCCESS: All IDs match perfectly!\n")
} else {
  cat("WARNING: Not all IDs match\n")
}

cat("\nYou can now use these files with PICRUSt2:\n")
cat("picrust2_pipeline.py -s otus_sequential.fna -i otu_table_sequential.tsv -o picrust2_output\n")
