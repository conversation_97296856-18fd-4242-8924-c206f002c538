#!/usr/bin/env Rscript
# Script to convert OTU table CSV to BIOM format for PICRUSt2

# Function to create BIOM format from CSV
create_biom_from_csv <- function(csv_file, biom_file) {
  cat("Converting OTU table to BIOM format for PICRUSt2...\n")
  
  # Read the CSV file
  otu_table <- read.csv(csv_file, header = TRUE, stringsAsFactors = FALSE, row.names = 1)
  cat("Read OTU table with", nrow(otu_table), "OTUs and", ncol(otu_table), "columns\n")
  
  # Remove taxonomy columns (usually the last few columns)
  # Look for common taxonomy column names
  taxonomy_cols <- grep("taxonomy|kingdom|phylum|class|order|family|genus|species", 
                       colnames(otu_table), ignore.case = TRUE)
  
  if (length(taxonomy_cols) > 0) {
    cat("Found", length(taxonomy_cols), "taxonomy columns, removing them\n")
    cat("Taxonomy columns:", colnames(otu_table)[taxonomy_cols], "\n")
    otu_data <- otu_table[, -taxonomy_cols]
  } else {
    cat("No taxonomy columns found, using all columns as sample data\n")
    otu_data <- otu_table
  }
  
  cat("Final OTU data matrix:", nrow(otu_data), "OTUs x", ncol(otu_data), "samples\n")
  
  # Convert to matrix (required for BIOM)
  otu_matrix <- as.matrix(otu_data)
  
  # Create a simple tab-delimited file that can be converted to BIOM
  # Add OTU_ID column back
  output_data <- data.frame(
    OTU_ID = rownames(otu_matrix),
    otu_matrix,
    stringsAsFactors = FALSE
  )
  
  # Write tab-delimited file
  temp_file <- "temp_otu_table.txt"
  write.table(output_data, temp_file, sep = "\t", quote = FALSE, row.names = FALSE)
  
  cat("Created temporary tab-delimited file:", temp_file, "\n")
  cat("You can convert this to BIOM format using:\n")
  cat("biom convert -i", temp_file, "-o", biom_file, "--table-type='OTU table' --to-hdf5\n")
  
  # Also create a simple TSV format that PICRUSt2 can read directly
  tsv_file <- gsub("\\.biom$", ".tsv", biom_file)
  
  # Create header with # for QIIME format
  cat("# Constructed from biom file\n", file = tsv_file)
  write.table(output_data, tsv_file, sep = "\t", quote = FALSE, row.names = FALSE, append = TRUE)
  
  cat("Created TSV file for PICRUSt2:", tsv_file, "\n")
  cat("This TSV file can be used directly with PICRUSt2 if BIOM tools are not available\n")
  
  return(list(
    temp_file = temp_file,
    tsv_file = tsv_file,
    n_otus = nrow(otu_data),
    n_samples = ncol(otu_data)
  ))
}

# Main execution
main <- function() {
  csv_file <- "OTU_table_taxonomy.csv"
  biom_file <- "otu_table.biom"
  
  if (!file.exists(csv_file)) {
    stop("CSV file not found: ", csv_file)
  }
  
  result <- create_biom_from_csv(csv_file, biom_file)
  
  cat("\n=== Summary ===\n")
  cat("Input CSV file:", csv_file, "\n")
  cat("Output TSV file:", result$tsv_file, "\n")
  cat("Temporary file for BIOM conversion:", result$temp_file, "\n")
  cat("Number of OTUs:", result$n_otus, "\n")
  cat("Number of samples:", result$n_samples, "\n")
  
  cat("\n=== Next Steps ===\n")
  cat("1. Use the TSV file directly with PICRUSt2, OR\n")
  cat("2. Convert to BIOM format if you have BIOM tools installed\n")
  cat("3. Use the filtered FASTA file: filtered_otus.fa\n")
}

# Run the main function
main()
