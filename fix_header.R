#!/usr/bin/env Rscript
# Fix the header issue in the OTU table

cat("Fixing OTU table header...\n")

# Read the clean table
otu_table <- read.table("otu_table_clean.tsv", header = TRUE, sep = "\t", stringsAsFactors = FALSE)

# Fix the first column name
colnames(otu_table)[1] <- "OTU_ID"

cat("Fixed column names. First column is now:", colnames(otu_table)[1], "\n")
cat("Table dimensions:", nrow(otu_table), "x", ncol(otu_table), "\n")

# Write the corrected table
write.table(otu_table, "otu_table_final.tsv", sep = "\t", quote = FALSE, row.names = FALSE)

cat("Created final OTU table: otu_table_final.tsv\n")

# Quick verification
table_ids <- otu_table$OTU_ID
fasta_lines <- readLines("filtered_otus.fna")
fasta_headers <- fasta_lines[grepl("^>", fasta_lines)]
fasta_ids <- sub("^>", "", fasta_headers)

matches <- sum(table_ids %in% fasta_ids)
cat("\nVerification:\n")
cat("Table OTUs:", length(table_ids), "\n")
cat("FASTA sequences:", length(fasta_ids), "\n")
cat("Matching OTUs:", matches, "\n")

if (matches > 0) {
  cat("\nSUCCESS! Files are ready for PICRUSt2:\n")
  cat("- OTU table: otu_table_final.tsv\n")
  cat("- Sequences: filtered_otus.fna\n")
  cat("\nRun PICRUSt2 with:\n")
  cat("picrust2_pipeline.py -s filtered_otus.fna -i otu_table_final.tsv -o picrust2_output\n")
} else {
  cat("\nStill no matches found. Let me show some examples:\n")
  cat("First 5 table IDs:", paste(head(table_ids, 5), collapse = ", "), "\n")
  cat("First 5 FASTA IDs:", paste(head(fasta_ids, 5), collapse = ", "), "\n")
}
