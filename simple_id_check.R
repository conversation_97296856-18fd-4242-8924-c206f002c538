#!/usr/bin/env Rscript
# Simple script to check the ID mismatch issue

cat("=== Checking OTU ID Mismatch ===\n")

# Read first few lines of each file
cat("OTU Table IDs (first 10):\n")
otu_table <- read.csv("OTU_table_taxonomy.csv", header = TRUE, stringsAsFactors = FALSE, nrows = 10)
print(otu_table[,1])

cat("\nFASTA IDs (first 10):\n")
fasta_lines <- readLines("cluster.cluster.otus.fa", n = 20)
fasta_headers <- fasta_lines[grepl("^>", fasta_lines)]
fasta_ids <- sub("^>", "", fasta_headers)
print(fasta_ids)

cat("\nProblem identified:\n")
cat("- OTU table uses IDs like: OTU7361, OTU1248, OTU73962\n")
cat("- FASTA file uses IDs like: OTU1, OTU2, OTU3\n")
cat("- These don't match, causing PICRUSt2 to find no overlaps\n")

cat("\nSolution needed:\n")
cat("We need to either:\n")
cat("1. Rename FASTA headers to match table IDs, OR\n")
cat("2. Find the correct mapping between them, OR\n")
cat("3. Rename table IDs to match FASTA IDs\n")
