#!/usr/bin/env python3
"""
Script to filter FASTA file based on OTU IDs present in an OTU table.
"""

import pandas as pd
import argparse
from Bio import SeqIO
from Bio.SeqRecord import SeqRecord
from Bio.Seq import Seq
import sys
import os

def read_otu_table(file_path):
    """
    Read OTU table from Excel file and extract OTU IDs.
    """
    try:
        # Try to read the Excel file
        df = pd.read_excel(file_path, sheet_name=0)
        print(f"Successfully read Excel file with shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        
        # Display first few rows to understand structure
        print("\nFirst 5 rows:")
        print(df.head())
        
        # Try to identify the OTU ID column
        # Common column names for OTU IDs
        possible_otu_columns = ['OTU', 'OTU_ID', 'OTUID', 'OTU ID', 'otu', 'otu_id', 'otuid']
        
        otu_column = None
        for col in possible_otu_columns:
            if col in df.columns:
                otu_column = col
                break
        
        # If no standard column found, use the first column
        if otu_column is None:
            otu_column = df.columns[0]
            print(f"No standard OTU column found, using first column: {otu_column}")
        else:
            print(f"Found OTU column: {otu_column}")
        
        # Extract OTU IDs
        otu_ids = set(df[otu_column].astype(str))
        print(f"Found {len(otu_ids)} unique OTU IDs")
        
        return otu_ids
        
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return None

def filter_fasta(fasta_file, otu_ids, output_file):
    """
    Filter FASTA file to keep only sequences with IDs present in otu_ids set.
    """
    kept_sequences = []
    total_sequences = 0
    
    print(f"Reading FASTA file: {fasta_file}")
    
    try:
        for record in SeqIO.parse(fasta_file, "fasta"):
            total_sequences += 1
            
            # Extract OTU ID from sequence header
            seq_id = record.id
            
            if seq_id in otu_ids:
                kept_sequences.append(record)
                
            # Progress indicator
            if total_sequences % 10000 == 0:
                print(f"Processed {total_sequences} sequences, kept {len(kept_sequences)}")
        
        print(f"Total sequences processed: {total_sequences}")
        print(f"Sequences kept: {len(kept_sequences)}")
        print(f"Sequences filtered out: {total_sequences - len(kept_sequences)}")
        
        # Write filtered sequences to output file
        if kept_sequences:
            SeqIO.write(kept_sequences, output_file, "fasta")
            print(f"Filtered sequences written to: {output_file}")
        else:
            print("No sequences matched the OTU table. Check if OTU IDs match between files.")
            
    except Exception as e:
        print(f"Error processing FASTA file: {e}")

def main():
    parser = argparse.ArgumentParser(description="Filter FASTA file based on OTU table")
    parser.add_argument("--otu_table", required=True, help="Path to OTU table (Excel file)")
    parser.add_argument("--fasta", required=True, help="Path to FASTA file to filter")
    parser.add_argument("--output", required=True, help="Path to output filtered FASTA file")
    
    args = parser.parse_args()
    
    # Check if input files exist
    if not os.path.exists(args.otu_table):
        print(f"Error: OTU table file not found: {args.otu_table}")
        sys.exit(1)
        
    if not os.path.exists(args.fasta):
        print(f"Error: FASTA file not found: {args.fasta}")
        sys.exit(1)
    
    # Read OTU IDs from table
    print("Reading OTU table...")
    otu_ids = read_otu_table(args.otu_table)
    
    if otu_ids is None:
        print("Failed to read OTU table")
        sys.exit(1)
    
    # Filter FASTA file
    print("\nFiltering FASTA file...")
    filter_fasta(args.fasta, otu_ids, args.output)
    
    print("\nFiltering complete!")

if __name__ == "__main__":
    main()
