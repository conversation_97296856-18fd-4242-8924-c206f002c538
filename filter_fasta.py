#!/usr/bin/env python3
"""
Script to filter FASTA file based on OTU IDs present in an OTU table (CSV format).
No external dependencies required - uses only Python standard library.
"""

import csv
import argparse
import sys
import os

def read_otu_table_csv(file_path):
    """
    Read OTU table from CSV file and extract OTU IDs.
    """
    otu_ids = set()

    try:
        with open(file_path, 'r', newline='', encoding='utf-8') as csvfile:
            # Read first line to get header
            first_line = csvfile.readline().strip()
            print(f"Header line: {first_line[:100]}...")  # Show first 100 chars

            # Reset file pointer to beginning
            csvfile.seek(0)

            # Create CSV reader
            reader = csv.reader(csvfile)
            header = next(reader)

            print(f"Found {len(header)} columns")
            print(f"First column (OTU ID column): {header[0]}")

            # Read all rows and extract OTU IDs from first column
            row_count = 0
            for row in reader:
                if row and len(row) > 0:  # Skip empty rows
                    otu_id = row[0].strip()
                    if otu_id:  # Skip empty OTU IDs
                        otu_ids.add(otu_id)
                        row_count += 1

                        # Progress indicator
                        if row_count % 10000 == 0:
                            print(f"Processed {row_count} rows...")

            print(f"Successfully read {row_count} OTU entries")
            print(f"Found {len(otu_ids)} unique OTU IDs")

            # Show some example OTU IDs
            example_otus = list(otu_ids)[:5]
            print(f"Example OTU IDs: {example_otus}")

            return otu_ids

    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return None

def read_fasta_simple(file_path):
    """
    Simple FASTA reader that yields (header, sequence) tuples.
    """
    with open(file_path, 'r') as f:
        header = None
        sequence = []

        for line in f:
            line = line.strip()
            if line.startswith('>'):
                # If we have a previous sequence, yield it
                if header is not None:
                    yield header, ''.join(sequence)

                # Start new sequence
                header = line[1:]  # Remove '>' character
                sequence = []
            else:
                sequence.append(line)

        # Don't forget the last sequence
        if header is not None:
            yield header, ''.join(sequence)

def filter_fasta(fasta_file, otu_ids, output_file):
    """
    Filter FASTA file to keep only sequences with IDs present in otu_ids set.
    """
    kept_count = 0
    total_count = 0

    print(f"Reading FASTA file: {fasta_file}")
    print(f"Looking for {len(otu_ids)} OTU IDs...")

    try:
        with open(output_file, 'w') as out_f:
            for header, sequence in read_fasta_simple(fasta_file):
                total_count += 1

                # Extract OTU ID from header (should be the full header in this case)
                otu_id = header.strip()

                if otu_id in otu_ids:
                    # Write the sequence to output file
                    out_f.write(f">{header}\n")
                    # Write sequence in lines of 80 characters
                    for i in range(0, len(sequence), 80):
                        out_f.write(sequence[i:i+80] + '\n')
                    kept_count += 1

                # Progress indicator
                if total_count % 10000 == 0:
                    print(f"Processed {total_count} sequences, kept {kept_count}")

        print(f"\nFiltering complete!")
        print(f"Total sequences processed: {total_count}")
        print(f"Sequences kept: {kept_count}")
        print(f"Sequences filtered out: {total_count - kept_count}")
        print(f"Filtered sequences written to: {output_file}")

        if kept_count == 0:
            print("\nWARNING: No sequences matched the OTU table!")
            print("This might indicate a mismatch between OTU IDs in the table and FASTA headers.")
            print("Please check that the OTU IDs match exactly between the two files.")

    except Exception as e:
        print(f"Error processing FASTA file: {e}")

def main():
    parser = argparse.ArgumentParser(description="Filter FASTA file based on OTU table (CSV format)")
    parser.add_argument("--otu_table", required=True, help="Path to OTU table (CSV file)")
    parser.add_argument("--fasta", required=True, help="Path to FASTA file to filter")
    parser.add_argument("--output", required=True, help="Path to output filtered FASTA file")

    args = parser.parse_args()

    # Check if input files exist
    if not os.path.exists(args.otu_table):
        print(f"Error: OTU table file not found: {args.otu_table}")
        sys.exit(1)

    if not os.path.exists(args.fasta):
        print(f"Error: FASTA file not found: {args.fasta}")
        sys.exit(1)

    # Read OTU IDs from CSV table
    print("Reading OTU table...")
    otu_ids = read_otu_table_csv(args.otu_table)

    if otu_ids is None or len(otu_ids) == 0:
        print("Failed to read OTU table or no OTU IDs found")
        sys.exit(1)

    # Filter FASTA file
    print("\nFiltering FASTA file...")
    filter_fasta(args.fasta, otu_ids, args.output)

if __name__ == "__main__":
    main()
