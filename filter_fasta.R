#!/usr/bin/env Rscript
# Script to filter FASTA file based on OTU IDs present in an OTU table (CSV format)
# No external packages required - uses only base R

# Function to read OTU IDs from CSV file
read_otu_ids <- function(csv_file) {
  cat("Reading OTU table from:", csv_file, "\n")
  
  # Read the CSV file
  otu_table <- read.csv(csv_file, header = TRUE, stringsAsFactors = FALSE)
  
  cat("CSV file dimensions:", nrow(otu_table), "rows x", ncol(otu_table), "columns\n")
  cat("First column name:", colnames(otu_table)[1], "\n")
  
  # Extract OTU IDs from first column
  otu_ids <- otu_table[, 1]
  
  # Remove any empty or NA values
  otu_ids <- otu_ids[!is.na(otu_ids) & otu_ids != ""]
  
  # Convert to character and get unique values
  otu_ids <- unique(as.character(otu_ids))
  
  cat("Found", length(otu_ids), "unique OTU IDs\n")
  cat("Example OTU IDs:", paste(head(otu_ids, 5), collapse = ", "), "\n")
  
  return(otu_ids)
}

# Function to read FASTA file and return headers and sequences
read_fasta <- function(fasta_file) {
  cat("Reading FASTA file:", fasta_file, "\n")
  
  # Read all lines from FASTA file
  lines <- readLines(fasta_file)
  
  # Find header lines (starting with >)
  header_indices <- which(grepl("^>", lines))
  
  cat("Found", length(header_indices), "sequences in FASTA file\n")
  
  # Initialize vectors to store headers and sequences
  headers <- character(length(header_indices))
  sequences <- character(length(header_indices))
  
  # Process each sequence
  for (i in 1:length(header_indices)) {
    # Get header (remove the > character)
    headers[i] <- sub("^>", "", lines[header_indices[i]])
    
    # Determine sequence start and end
    seq_start <- header_indices[i] + 1
    if (i < length(header_indices)) {
      seq_end <- header_indices[i + 1] - 1
    } else {
      seq_end <- length(lines)
    }
    
    # Concatenate sequence lines
    if (seq_start <= seq_end) {
      sequences[i] <- paste(lines[seq_start:seq_end], collapse = "")
    } else {
      sequences[i] <- ""
    }
    
    # Progress indicator
    if (i %% 10000 == 0) {
      cat("Processed", i, "sequences\n")
    }
  }
  
  return(list(headers = headers, sequences = sequences))
}

# Function to write FASTA file
write_fasta <- function(headers, sequences, output_file) {
  cat("Writing filtered sequences to:", output_file, "\n")
  
  # Open file for writing
  con <- file(output_file, "w")
  
  for (i in 1:length(headers)) {
    # Write header
    writeLines(paste0(">", headers[i]), con)
    
    # Write sequence in lines of 80 characters
    seq <- sequences[i]
    seq_length <- nchar(seq)
    
    if (seq_length > 0) {
      for (start in seq(1, seq_length, 80)) {
        end <- min(start + 79, seq_length)
        writeLines(substr(seq, start, end), con)
      }
    }
  }
  
  close(con)
  cat("Successfully wrote", length(headers), "sequences to", output_file, "\n")
}

# Main filtering function
filter_fasta_by_otu <- function(csv_file, fasta_file, output_file) {
  cat("=== FASTA Filtering Script ===\n")
  cat("OTU table (CSV):", csv_file, "\n")
  cat("Input FASTA:", fasta_file, "\n")
  cat("Output FASTA:", output_file, "\n\n")
  
  # Check if input files exist
  if (!file.exists(csv_file)) {
    stop("Error: CSV file not found: ", csv_file)
  }
  
  if (!file.exists(fasta_file)) {
    stop("Error: FASTA file not found: ", fasta_file)
  }
  
  # Read OTU IDs from CSV
  cat("Step 1: Reading OTU IDs from CSV file\n")
  otu_ids <- read_otu_ids(csv_file)
  
  if (length(otu_ids) == 0) {
    stop("Error: No OTU IDs found in CSV file")
  }
  
  # Read FASTA file
  cat("\nStep 2: Reading FASTA file\n")
  fasta_data <- read_fasta(fasta_file)
  
  if (length(fasta_data$headers) == 0) {
    stop("Error: No sequences found in FASTA file")
  }
  
  # Filter sequences
  cat("\nStep 3: Filtering sequences\n")
  cat("Looking for matches between", length(otu_ids), "OTU IDs and", length(fasta_data$headers), "FASTA sequences\n")
  
  # Find which FASTA headers match OTU IDs
  matches <- fasta_data$headers %in% otu_ids
  
  # Get filtered data
  filtered_headers <- fasta_data$headers[matches]
  filtered_sequences <- fasta_data$sequences[matches]
  
  cat("Sequences kept:", length(filtered_headers), "\n")
  cat("Sequences filtered out:", sum(!matches), "\n")
  
  if (length(filtered_headers) == 0) {
    cat("\nWARNING: No sequences matched the OTU table!\n")
    cat("This might indicate a mismatch between OTU IDs and FASTA headers.\n")
    cat("\nFirst few OTU IDs from table:\n")
    print(head(otu_ids, 10))
    cat("\nFirst few FASTA headers:\n")
    print(head(fasta_data$headers, 10))
    cat("\nPlease check that the OTU IDs match exactly between the two files.\n")
    return(FALSE)
  }
  
  # Write filtered FASTA
  cat("\nStep 4: Writing filtered FASTA file\n")
  write_fasta(filtered_headers, filtered_sequences, output_file)
  
  cat("\n=== Filtering Complete! ===\n")
  cat("Summary:\n")
  cat("- Total sequences in input FASTA:", length(fasta_data$headers), "\n")
  cat("- OTU IDs in table:", length(otu_ids), "\n")
  cat("- Sequences kept:", length(filtered_headers), "\n")
  cat("- Sequences removed:", sum(!matches), "\n")
  cat("- Output file:", output_file, "\n")
  
  return(TRUE)
}

# Main execution
main <- function() {
  # File paths (you can modify these if needed)
  csv_file <- "OTU_table_taxonomy.csv"
  fasta_file <- "cluster.cluster.otus.fa"
  output_file <- "filtered_otus.fa"
  
  # Run the filtering
  tryCatch({
    success <- filter_fasta_by_otu(csv_file, fasta_file, output_file)
    if (success) {
      cat("\nScript completed successfully!\n")
    }
  }, error = function(e) {
    cat("Error occurred:", e$message, "\n")
  })
}

# Run the main function
main()
