#!/usr/bin/env Rscript
# Create clean files for PICRUSt2

cat("Creating clean files for PICRUSt2...\n")

# Read OTU table more carefully
cat("Reading OTU table...\n")
otu_table <- read.csv("OTU_table_taxonomy.csv", header = TRUE, stringsAsFactors = FALSE)

cat("Original table dimensions:", nrow(otu_table), "x", ncol(otu_table), "\n")
cat("Column names:", paste(head(colnames(otu_table), 10), collapse = ", "), "...\n")

# Remove taxonomy columns (last few columns that contain text)
# Find columns that are likely taxonomy
sample_cols <- grep("^SMT[0-9]+$", colnames(otu_table))
cat("Found", length(sample_cols), "sample columns (SMT001, SMT002, etc.)\n")

# Keep only OTU_ID column and sample columns
if (length(sample_cols) > 0) {
  clean_table <- otu_table[, c(1, sample_cols)]
} else {
  # Fallback: remove known taxonomy columns
  taxonomy_cols <- grep("taxonomy|kingdom|phylum|class|order|family|genus", 
                       colnames(otu_table), ignore.case = TRUE)
  if (length(taxonomy_cols) > 0) {
    clean_table <- otu_table[, -taxonomy_cols]
  } else {
    clean_table <- otu_table
  }
}

cat("Clean table dimensions:", nrow(clean_table), "x", ncol(clean_table), "\n")

# Check for and remove any empty or problematic columns
clean_table <- clean_table[, !is.na(colnames(clean_table)) & colnames(clean_table) != ""]

# Remove any rows with empty OTU IDs
clean_table <- clean_table[!is.na(clean_table[,1]) & clean_table[,1] != "", ]

cat("Final table dimensions:", nrow(clean_table), "x", ncol(clean_table), "\n")

# Create clean TSV file for PICRUSt2
cat("Writing clean OTU table...\n")
write.table(clean_table, "otu_table_clean.tsv", sep = "\t", 
           quote = FALSE, row.names = FALSE)

# Check the FASTA file
cat("Checking FASTA file...\n")
fasta_lines <- readLines("filtered_otus.fna")
fasta_headers <- fasta_lines[grepl("^>", fasta_lines)]
fasta_ids <- sub("^>", "", fasta_headers)

cat("FASTA file has", length(fasta_ids), "sequences\n")

# Check overlap
table_ids <- clean_table[,1]
matches <- sum(table_ids %in% fasta_ids)
reverse_matches <- sum(fasta_ids %in% table_ids)

cat("\nOverlap check:\n")
cat("Table OTUs:", length(table_ids), "\n")
cat("FASTA sequences:", length(fasta_ids), "\n")
cat("Table OTUs found in FASTA:", matches, "\n")
cat("FASTA sequences found in table:", reverse_matches, "\n")

# Show examples
cat("\nFirst 5 table OTU IDs:", paste(head(table_ids, 5), collapse = ", "), "\n")
cat("First 5 FASTA IDs:", paste(head(fasta_ids, 5), collapse = ", "), "\n")

if (matches > 0) {
  cat("\nSUCCESS: Found", matches, "matching OTUs!\n")
  cat("Files ready for PICRUSt2:\n")
  cat("- OTU table: otu_table_clean.tsv\n")
  cat("- Sequences: filtered_otus.fna\n")
} else {
  cat("\nPROBLEM: No matching OTUs found\n")
  cat("This explains the PICRUSt2 error\n")
  
  # Show some non-matching examples
  cat("Some table IDs not in FASTA:\n")
  missing <- table_ids[!table_ids %in% fasta_ids]
  print(head(missing, 10))
  
  cat("Some FASTA IDs not in table:\n")
  extra <- fasta_ids[!fasta_ids %in% table_ids]
  print(head(extra, 10))
}
